# -*- coding: utf-8 -*-

import time
from typing import Optional

from common.logger import Logger
from utils.http_utils import HttpUtils
from config.tikhub_config import tikhub_config
import requests


class TikhubKuaiShouClient:
    def __init__(self,
                 logger_,
                 max_retries: int = 3,
                 retry_delay: int = 2):

        self.base_url = tikhub_config.get("base_url")
        self.authorization = tikhub_config.get("api_key")
        self.headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {self.authorization}"
        }
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self.logger_ = logger_

    def __process_request(self, url: str, headers: dict, method: str, params: dict = None) -> Optional[dict]:

        for attempt in range(self._max_retries):
            try:
                if method == "GET":
                    response = HttpUtils.get(url=url, headers=headers)
                else:
                    response = HttpUtils.post(url, data=params, headers=headers)

                if not response or \
                        not response.text.strip() or \
                        not response.content:
                    error_message = "第 {0} 次响应内容为空, 状态码: {1}, URL:{2}".format(attempt + 1,
                                                                                         response.status_code,
                                                                                         response.url)
                    if self.logger_:
                        self.logger_.info(error_message)

                    if attempt < self._max_retries - 1:
                        if self.logger_:
                            self.logger_.info(f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试...")
                        time.sleep(self._retry_delay)
                    continue

                response.raise_for_status()
                return response.json()

            except Exception as e:
                if self.logger_:
                    self.logger_.error(f"第 {attempt + 1} 次请求发生异常: {str(e)}, URL: {url}")
                if attempt < self._max_retries - 1:
                    if self.logger_:
                        self.logger_.info(f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试...")
                    time.sleep(self._retry_delay)
                else:
                    return None

        if self.logger_:
            self.logger_.error(f"所有 {self._max_retries} 次重试都失败了，URL: {url}")

        return None

    def app_fetch_one_video_v1(self, work_id: str) -> dict:
        endpoint = "/api/v1/kuaishou/app/fetch_one_video"

        url: str = self.base_url + f"{endpoint}?photo_id={work_id}"

        search_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return search_result
