# -*- coding: utf-8 -*-

import base64
import hashlib
import hmac
import json
import urllib.parse

import requests  # Make sure to install this library: pip install requests


class DingTalkLinker:
    def __init__(self, logger_):
        self.logger_ = logger_

    def calculate_hmac_sha256_signature(self, data_to_sign_str: str, secret_key_str: str) -> str:
        # 将密钥和数据转换为字节串 (UTF-8编码)
        secret_key_bytes = secret_key_str.encode('utf-8')
        data_to_sign_bytes = data_to_sign_str.encode('utf-8')

        # 计算 HMAC-SHA256 哈希值
        hmac_obj = hmac.new(secret_key_bytes, data_to_sign_bytes, hashlib.sha256)
        raw_hmac_digest = hmac_obj.digest()  # 获取原始字节摘要

        # 对原始哈希摘要进行 Base64 编码
        base64_encoded_hmac_bytes = base64.b64encode(raw_hmac_digest)
        base64_encoded_hmac_str = base64_encoded_hmac_bytes.decode('utf-8')  # 转换为字符串

        # 对 Base64 编码后的字符串进行 URL 编码
        # urllib.parse.quote_plus 会将空格转为'+', 并对其他特殊字符如'+', '/', '='进行编码
        url_encoded_signature = urllib.parse.quote_plus(base64_encoded_hmac_str)
        return url_encoded_signature

    def generate_dingtalk_signature(self, json_payload_dict: dict, secret_key_str: str, timestamp_ms: int) -> str:
        # 1. 对 JSON 请求体按 key 排序并转换为紧凑的 JSON 字符串。
        sorted_json_str = json.dumps(json_payload_dict, sort_keys=True, separators=(',', ':'))

        # 2. 构建待签名字符串: timestamp + 排序后的JSON字符串。
        string_to_sign = str(timestamp_ms) + sorted_json_str

        # 3. 使用 HMAC-SHA256 生成签名，并进行 Base64 和 URL 编码。
        signature = self.calculate_hmac_sha256_signature(string_to_sign, secret_key_str)

        return signature

    def send_dingtalk_webhook_post(self, webhook_url: str, payload_data_dict: dict):
        # 准备请求头
        headers = {
            'Content-Type': 'application/json;charset=utf-8'  # 指定UTF-8编码
        }

        try:
            response = requests.post(webhook_url, json=payload_data_dict, headers=headers)
            response.raise_for_status()  # 如果请求失败 (状态码 4xx 或 5xx)，则抛出 HTTPError 异常

            self.logger_.info("请求发送成功!")
            self.logger_.info(f"响应状态码: {response.status_code}")
            self.logger_.info(f"响应内容: {response.text}")

            return response
        except requests.exceptions.HTTPError as http_err:
            self.logger_.error(f"HTTP 错误: {http_err}")
            self.logger_.error(f"响应状态码: {http_err.response.status_code if http_err.response else 'N/A'}")
            self.logger_.error(f"响应内容 (如果存在): {http_err.response.text if http_err.response else 'N/A'}")
        except requests.exceptions.RequestException as req_err:
            self.logger_.error(f"请求发送失败 (例如网络问题): {req_err}")

        return None
