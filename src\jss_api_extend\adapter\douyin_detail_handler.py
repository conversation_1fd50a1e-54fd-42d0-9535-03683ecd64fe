# -*- coding: utf-8 -*-

import time
import asyncio
import concurrent.futures
from typing import Optional

from dateutil import parser
from tikhub import Client

from ..client.qb.handler import Handler
from ..client.qb.models import Platform
from ..config.tikhub_config import tikhub_config
from .models.author_work import AuthorWork
from ..utils.region_code_converter import RegionCodeConverter
from ..utils.string_utils import StringUtils
from ..utils.time_utils import TimeUtils


class DouyinDetailHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.client = Client(base_url=tikhub_config.get('base_url'),
                             api_key=tikhub_config.get("api_key"),
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.DOUYIN)
        self._converter = RegionCodeConverter()

    async def query_article_detail(self, note_id) -> Optional[AuthorWork]:
        try:
            should_retry, work_article = await self._query_article_detail_by_tikhub(note_id)
            if not should_retry:
                return work_article

            item = self._query_article_detail_by_qingbo(note_id)
            if item:
                item_dict = item.to_dict()
                item_dict["id"] = note_id

                return self._generate_qingbo_article(item_dict)
        except Exception as e:
            self.logger_.error(f"抖音获取详情失败{e}")
            return None

    def _query_article_detail_by_qingbo(self, note_id):
        work_url = "https://www.douyin.com/video/" + note_id
        item = self.qingbo_handler.query_article_detail(key=work_url)

        return item

    async def _query_article_detail_by_tikhub(self, note_id):
        article, should_retry = await self._query_article_detail_by_tikhub_inner(note_id)
        if article is None and should_retry:
            time.sleep(1)
            article, should_retry = await self._query_article_detail_by_tikhub_inner(note_id)

        if article is None and should_retry:
            time.sleep(1)
            article, should_retry = await self._query_article_detail_by_tikhub_inner(note_id)

        if article is None:
            return False, None

        return should_retry, self._generate_tiktok_article(note_id, article.get("data"))

    async def _query_article_detail_by_tikhub_inner(self, note_id):
        """
        查询抖音作品详情
        返回: (article_data, should_retry)
        - article_data: 作品数据，成功时返回数据，失败时返回None
        - should_retry: 是否应该重试，True表示可以重试，False表示不应重试（如作品不存在）
        """
        article = await self.client.DouyinAppV3.fetch_one_video(note_id)
        if not article or article.get("code") != 200:
            return None, True
        if "data" not in article:
            return None, True
        _article_data = article["data"]

        if "aweme_detail" not in _article_data:
            self.logger_.error(f"作品资源不存在: {note_id}")
            return None, False

        return article, False

    def _generate_tiktok_article(self, note_id, data):
        try:
            note = data.get("aweme_detail")
            if note is None:
                return None

            collect_count = comment_count = share_count = like_count = 0
            publish_day = ''

            user = note.get("author")
            author_name = self._parse_json_str(user, 'nickname')
            author_id = self._parse_json_str(user, 'sec_uid')
            author_url = "https://www.douyin.com/user/" + author_id

            author_avatar = ''
            try:
                avatar = user.get("avatar_thumb")
                url_list = avatar.get("url_list")
                author_avatar = url_list[0]
            except Exception as e1:
                print(e1)

            work_id = note_id
            url = "https://www.douyin.com/video/" + note_id

            title = note.get('preview_title', '')

            content = note.get('desc', '')

            statistics = note.get('statistics')
            if statistics is not None:
                collect_count = statistics.get('collect_count', 0)
                comment_count = statistics.get('comment_count', 0)
                share_count = statistics.get('share_count', 0)
                like_count = statistics.get('digg_count', 0)

            city_code = note.get('city', '')
            ip_location = self._convert_code_to_name(city_code)

            publish_time = TimeUtils.ts_to_str(note.get('create_time'))
            try:
                date_obj = parser.parse(publish_time)
                publish_day = date_obj.strftime("%Y-%m-%d")
            except Exception as e2:
                print(e2)

            thumbnail_link = ''
            video_url = ''
            try:
                video = note.get('video')
                cover = video.get('cover')
                cover_url_list = cover.get("url_list")
                thumbnail_link = cover_url_list[0]

                play_addr = video.get("play_addr")
                video_play_url_list = play_addr.get("url_list")
                for video_play_url in video_play_url_list:
                    if "amemv.com/aweme/v1/play" in video_play_url:
                        video_url = video_play_url
                        break

            except Exception as e:
                pass

            return AuthorWork(_id=0, platform='dy', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls=thumbnail_link, video_urls=video_url,
                              music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip=ip_location, read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")

    def _generate_qingbo_article(self, item: dict) -> Optional[AuthorWork]:
        try:
            author_name = item.get('media_name', '')
            author_id = item.get('media_identity', '')
            author_avatar = item.get('media_picurl', '')
            author_url = "https://www.douyin.com/user/" + author_id

            work_id = item.get('id', '')
            url = "https://www.douyin.com/video/" + work_id
            title = item.get('news_title', '')
            content = item.get('news_content')
            collect_count = item.get('news_collect_cnt')
            comment_count = item.get('news_comment_count')
            share_count = item.get('news_reposts_count')
            like_count = item.get('news_like_count')
            publish_time = item.get('news_posttime')

            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")
            thumbnail_link = item.get('news_headimg_url', '')

            return AuthorWork(_id=0, platform='dy', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )
        except Exception as e:
            self.logger_.error(f"_generate_qingbo_article {StringUtils.obj_2_json_string(item)}, {e}")
            return None

    def _convert_code_to_name(self, code):
        if code is None:
            return ""

        d = self._converter.convert(code)
        province = d.get("province", "")
        city = d.get("city", "")

        if not city and len(city) > 0:
            return city

        return province

    def _parse_json_str(self, obj, key):
        if obj is None:
            return ""

        return obj.get(key, "")


if __name__ == "__main__":
    import asyncio
    from ..common.env import Env

    async def main():
        logger_ = Env().get_main_logger()
        dy_handler = DouyinDetailHandler(logger_)

        work_article = await dy_handler.query_article_detail('7521675067002162467')
        print(work_article)

    asyncio.run(main())
