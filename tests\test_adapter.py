# -*- coding: utf-8 -*-
"""
Tests for adapter module.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from jss_api_extend.adapter import (
    <PERSON><PERSON>in<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    KuaiShou<PERSON><PERSON>,
    TiktokSearchHandler
)


class TestDouyinDetailHandler:
    """Test DouyinDetailHandler class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.handler = DouyinDetailHandler(self.mock_logger)
    
    def test_init(self):
        """Test handler initialization."""
        assert self.handler.logger_ == self.mock_logger
        assert self.handler.client is not None
        assert self.handler._converter is not None
    
    @pytest.mark.asyncio
    async def test_query_article_detail_success(self):
        """Test successful article detail query."""
        # Mock the API response
        with patch.object(self.handler, '_query_article_detail_by_tikhub') as mock_query:
            mock_work = Mock()
            mock_work.title = "Test Title"
            mock_work.author_name = "Test Author"
            mock_query.return_value = (True, mock_work)
            
            result = await self.handler.query_article_detail("test_work_id")
            
            assert result == mock_work
            mock_query.assert_called_once_with("test_work_id")
    
    @pytest.mark.asyncio
    async def test_query_article_detail_failure(self):
        """Test failed article detail query."""
        with patch.object(self.handler, '_query_article_detail_by_tikhub') as mock_query:
            mock_query.return_value = (False, None)
            
            result = await self.handler.query_article_detail("test_work_id")
            
            assert result is None


class TestXhsDetailHandler:
    """Test XhsDetailHandler class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.handler = XhsDetailHandler(self.mock_logger)
    
    def test_init(self):
        """Test handler initialization."""
        assert self.handler.logger_ == self.mock_logger
        assert self.handler.tikhub_xhs_client is not None
    
    def test_query_article_detail_success(self):
        """Test successful article detail query."""
        with patch.object(self.handler, '_query_article_detail_by_qingbo') as mock_qb:
            with patch.object(self.handler, '_query_article_detail_by_tikhub') as mock_th:
                mock_work = Mock()
                mock_work.title = "Test XHS Note"
                mock_qb.return_value = (mock_work, False)
                
                result = self.handler.query_article_detail("test_note_id")
                
                assert result == mock_work
                mock_qb.assert_called_once()
    
    def test_query_article_detail_fallback_to_tikhub(self):
        """Test fallback to tikhub when qingbo fails."""
        with patch.object(self.handler, '_query_article_detail_by_qingbo') as mock_qb:
            with patch.object(self.handler, '_query_article_detail_by_tikhub') as mock_th:
                mock_work = Mock()
                mock_work.title = "Test XHS Note from Tikhub"
                mock_qb.return_value = (None, True)  # Failed, should retry
                mock_th.return_value = (True, mock_work)
                
                result = self.handler.query_article_detail("test_note_id")
                
                assert result == mock_work
                mock_qb.assert_called_once()
                mock_th.assert_called_once()


class TestKuaiShouDetailHandler:
    """Test KuaiShouDetailHandler class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.handler = KuaiShouDetailHandler(self.mock_logger)
    
    def test_init(self):
        """Test handler initialization."""
        assert self.handler.logger_ == self.mock_logger
        assert self.handler.client is not None
        assert self.handler._converter is not None
        assert self.handler.tikhub_ks_client is not None
    
    def test_query_article_detail_app_v1_success(self):
        """Test successful article detail query."""
        test_url = "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6"
        
        with patch.object(self.handler, '_query_article_detail_by_tikhub') as mock_query:
            mock_work = Mock()
            mock_work.title = "Test KS Video"
            mock_query.return_value = (True, mock_work)
            
            result = self.handler.query_article_detail_app_v1(test_url)
            
            assert result == mock_work
    
    def test_query_article_detail_app_v1_failure(self):
        """Test failed article detail query."""
        test_url = "https://www.kuaishou.com/short-video/invalid"
        
        with patch.object(self.handler, '_query_article_detail_by_tikhub') as mock_query:
            mock_query.return_value = (False, None)
            
            result = self.handler.query_article_detail_app_v1(test_url)
            
            assert result is None


class TestXhsCommentHandler:
    """Test XhsCommentHandler class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.handler = XhsCommentHandler(self.mock_logger)
    
    def test_init(self):
        """Test handler initialization."""
        assert self.handler.logger_ == self.mock_logger
        assert self.handler.tikhub_xhs_client is not None
    
    def test_query_article_comment_success(self):
        """Test successful comment query."""
        with patch.object(self.handler, '_query_article_comment_by_qingbo') as mock_qb:
            with patch.object(self.handler, '_query_article_comment_by_tikhub') as mock_th:
                mock_comments = [Mock(), Mock()]
                mock_top_comments = [Mock()]
                mock_qb.return_value = (True, mock_comments, mock_top_comments)
                
                success, all_comments, top_comments = self.handler.query_article_comment(
                    "test_note_id", count=50
                )
                
                assert success is True
                assert all_comments == mock_comments
                assert top_comments == mock_top_comments


# Integration tests (require actual API keys)
@pytest.mark.integration
class TestIntegration:
    """Integration tests that require actual API configuration."""
    
    @pytest.mark.skip(reason="Requires actual API keys")
    @pytest.mark.asyncio
    async def test_real_douyin_api(self):
        """Test with real Douyin API (requires API key)."""
        # This test would require actual API keys and should be run separately
        pass
    
    @pytest.mark.skip(reason="Requires actual API keys")
    def test_real_xhs_api(self):
        """Test with real XHS API (requires API key)."""
        # This test would require actual API keys and should be run separately
        pass


if __name__ == "__main__":
    pytest.main([__file__])
