# -*- coding: utf-8 -*-

from bitable.dingtalk_bitable_notifier import DingTalkBitableNotifier
from bitable.taotian_dingtalk_bitable_notifier import TaoTianDingTalkBitableNotifier


class DingtalkBitableFactory:

    @staticmethod
    def create_object(company, logger_, app_key, app_secret, agent_id):
        if company == "水云兔":
            return DingTalkBitableNotifier(logger_, app_key, app_secret, agent_id)
        if company == "大毛":
            return DingTalkBitableNotifier(logger_, app_key, app_secret, agent_id)
        elif company == "淘天":
            return TaoTianDingTalkBitableNotifier(logger_, app_key, app_secret, agent_id)
        else:
            raise ValueError(f"未知的汽车类型: {company}")
