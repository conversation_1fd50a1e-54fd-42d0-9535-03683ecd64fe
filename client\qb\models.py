# -*- coding: utf-8 -*-

from dataclasses import dataclass, asdict
from enum import Enum
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from typing import Optional, Dict, Any


class Platform(Enum):
    XIAOHONGSHU = "xhs"
    DOUYIN = "dy"
    WXVIDEO = "wxvideo"


class SortOrder(Enum):
    ASC = "asc"
    DESC = "desc"


@dataclass
class Pagination:
    page: int
    limit: int
    offset: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Pagination':
        return cls(
            page=int(data.get('page', 1)),
            limit=int(data.get('limit', 20)),
            offset=int(data.get('offset', 0))
        )


@dataclass
class ApiConfig:
    project_id: int
    sign: str
    base_url: str = "http://projects-databus.gsdata.cn:7777/api-project/service"


@dataclass
class ArticleDetail:
    news_uuid: str
    news_url: str
    platform_name: str
    media_identity: str
    media_id: str
    media_name: str
    news_video_urls: Optional[str] = None
    news_img_urls: Optional[str] = None
    news_title: str = ""
    news_digest: str = ""
    news_posttime: str = ""
    news_content_ip_location: str = ""
    news_content: str = ""
    news_read_count: int = 0
    news_like_count: int = 0
    news_comment_count: int = 0
    news_collect_cnt: int = 0
    news_reposts_count: int = 0
    news_headimg_url: str = ""
    media_picurl: str = ""
    music_url: str = ""
    music_author_name: str = ""
    music_id: str = ""
    music_name: str = ""

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ArticleDetail':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

    def to_dict(self) -> Dict[str, Any]:
        """
        转为 dict
        @return:
        """
        return asdict(self)



@dataclass
class Comment:
    news_url: str
    comment_user: str
    comment_user_id: str
    comment_content: str
    comment_posttime: str
    comment_like_count: int
    comment_ip_location: str
    first_cid: Optional[str] = field(default="")
    has_sub: Optional[str] = field(default="")
    is_sub: Optional[str] = field(default="")
    sec_uid: Optional[str] = field(default=None)
    news_parent_id: Optional[str] = field(default=None)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Comment':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})


@dataclass
class CommentResponse:
    has_more: bool
    cursor: str
    comments: List[Comment]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CommentResponse':
        comments_data = data.get('list', [])
        return cls(
            has_more=bool(data.get('has_more', 0)),
            cursor=str(data.get('cursor', '')),
            comments=[Comment.from_dict(comment) for comment in comments_data]
        )


@dataclass
class ApiResponse:
    success: bool
    code: int
    message: str
    data: Any

    @classmethod
    def from_dict(cls, response_data: Dict[str, Any]) -> 'ApiResponse':
        # Handle both response formats
        if 'data' in response_data and isinstance(response_data['data'], dict):
            data = response_data.get('data', {})
            return cls(
                success=response_data.get('success', False),
                code=data.get('code', 0),
                message=data.get('message', ''),
                data=data.get('data', {})
            )
        else:
            # Treat the entire response as data if it doesn't match the expected format
            return cls(
                success=True,
                code=200,
                message='',
                data=response_data
            )


@dataclass
class DouyinGroupArticle:
    news_uuid: str
    douyin_code: str
    douyin_id: str
    douyin_name: str
    news_url: str
    news_title: str
    news_content: str
    news_img: str
    music_url: Optional[str]
    download_url: str
    like_nums: str
    comment_nums: str
    share_nums: str
    collection_count: str
    news_posttime: str
    music_author_name: str
    music_name: str
    news_content_ip_location: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DouyinGroupArticle':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})


@dataclass
class DouyinGroupResponse:
    articles: List[DouyinGroupArticle]
    num_found: str
    pagination: Pagination

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DouyinGroupResponse':
        return cls(
            articles=[DouyinGroupArticle.from_dict(item) for item in data.get('list', [])],
            num_found=str(data.get('numFound', '0')),
            pagination=Pagination.from_dict(data.get('pagination', {}))
        )


@dataclass
class WxVideoGroupArticle:
    wxvideo_id: str
    wxvideo_name: str
    news_uuid: str
    news_title: str
    news_posttime: str
    news_url: str
    news_headimg_url: str
    news_content: str
    news_comment_count: str
    news_like_count: str
    news_reposts_count: str
    eid: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WxVideoGroupArticle':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})


@dataclass
class WxVideoGroupResponse:
    articles: List[WxVideoGroupArticle]
    num_found: str
    pagination: Pagination

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WxVideoGroupResponse':
        return cls(
            articles=[WxVideoGroupArticle.from_dict(item) for item in data.get('list', [])],
            num_found=str(data.get('numFound', '0')),
            pagination=Pagination.from_dict(data.get('pagination', {}))
        )


@dataclass
class XiaohongshuGroupArticle:
    xiaohongshu_id: str
    xiaohongshu_name: str
    xiaohongshu_identity: str
    news_uuid: str
    news_title: str
    news_posttime: str
    news_url: str
    news_headimg_url: str
    news_content: str
    news_comment_count: str
    news_like_count: str
    news_collect_count: str
    news_fetch_time: str
    news_is_origin: str
    is_video: str
    news_img_urls: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'XiaohongshuGroupArticle':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})


@dataclass
class XiaohongshuGroupResponse:
    articles: List[XiaohongshuGroupArticle]
    num_found: str
    pagination: Pagination

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'XiaohongshuGroupResponse':
        return cls(
            articles=[XiaohongshuGroupArticle.from_dict(item) for item in data.get('list', [])],
            num_found=str(data.get('numFound', '0')),
            pagination=Pagination.from_dict(data.get('pagination', {}))
        )
