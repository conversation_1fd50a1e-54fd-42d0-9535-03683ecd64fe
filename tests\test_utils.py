# -*- coding: utf-8 -*-
"""
Tests for utils module.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from jss_api_extend.utils import (
    StringUtils,
    TimeUtils,
    UrlUtils,
    UUIDUtils,
    RegionCodeConverter
)


class TestStringUtils:
    """Test StringUtils class."""
    
    def test_obj_2_json_string(self):
        """Test object to JSON string conversion."""
        test_obj = {"key": "value", "number": 123}
        result = StringUtils.obj_2_json_string(test_obj)
        
        assert isinstance(result, str)
        assert "key" in result
        assert "value" in result
    
    def test_obj_2_json_string_with_none(self):
        """Test object to JSON string conversion with None."""
        result = StringUtils.obj_2_json_string(None)
        assert result == "null"
    
    def test_is_empty_string(self):
        """Test empty string check."""
        assert StringUtils.is_empty_string("") is True
        assert StringUtils.is_empty_string("   ") is True
        assert StringUtils.is_empty_string(None) is True
        assert StringUtils.is_empty_string("test") is False
    
    def test_clean_string(self):
        """Test string cleaning."""
        test_string = "  test string  \n\t"
        result = StringUtils.clean_string(test_string)
        assert result == "test string"


class TestTimeUtils:
    """Test TimeUtils class."""
    
    def test_get_current_timestamp(self):
        """Test current timestamp generation."""
        timestamp = TimeUtils.get_current_timestamp()
        assert isinstance(timestamp, int)
        assert timestamp > 0
    
    def test_timestamp_to_datetime(self):
        """Test timestamp to datetime conversion."""
        timestamp = 1609459200  # 2021-01-01 00:00:00 UTC
        result = TimeUtils.timestamp_to_datetime(timestamp)
        
        assert isinstance(result, datetime)
        assert result.year == 2021
        assert result.month == 1
        assert result.day == 1
    
    def test_datetime_to_string(self):
        """Test datetime to string conversion."""
        dt = datetime(2021, 1, 1, 12, 0, 0)
        result = TimeUtils.datetime_to_string(dt)
        
        assert isinstance(result, str)
        assert "2021" in result
        assert "01" in result
    
    def test_parse_time_string(self):
        """Test time string parsing."""
        time_string = "2021-01-01 12:00:00"
        result = TimeUtils.parse_time_string(time_string)
        
        assert isinstance(result, datetime)
        assert result.year == 2021
        assert result.month == 1
        assert result.day == 1
        assert result.hour == 12


class TestUrlUtils:
    """Test UrlUtils class."""
    
    def test_get_kuaishou_work_id(self):
        """Test Kuaishou work ID extraction."""
        test_urls = [
            "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6",
            "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6?authorId=123",
        ]
        
        for url in test_urls:
            result = UrlUtils.get_kuaishou_work_id(url)
            assert result == "3xk4g2yryfjpin6"
    
    def test_get_kuaishou_work_id_invalid(self):
        """Test Kuaishou work ID extraction with invalid URL."""
        invalid_url = "https://invalid-url.com"
        result = UrlUtils.get_kuaishou_work_id(invalid_url)
        assert result is None or result == ""
    
    def test_extract_douyin_work_id(self):
        """Test Douyin work ID extraction."""
        test_url = "https://www.douyin.com/video/7123456789012345678"
        result = UrlUtils.extract_douyin_work_id(test_url)
        assert result == "7123456789012345678"
    
    def test_extract_xhs_note_id(self):
        """Test XHS note ID extraction."""
        test_url = "https://www.xiaohongshu.com/explore/6863e1450000000024008d2b"
        result = UrlUtils.extract_xhs_note_id(test_url)
        assert result == "6863e1450000000024008d2b"
    
    def test_is_valid_url(self):
        """Test URL validation."""
        valid_urls = [
            "https://www.example.com",
            "http://example.com",
            "https://subdomain.example.com/path?query=value"
        ]
        
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",  # depending on implementation
            "",
            None
        ]
        
        for url in valid_urls:
            assert UrlUtils.is_valid_url(url) is True
        
        for url in invalid_urls:
            assert UrlUtils.is_valid_url(url) is False


class TestUUIDUtils:
    """Test UUIDUtils class."""
    
    def test_generate_uuid(self):
        """Test UUID generation."""
        uuid1 = UUIDUtils.generate_uuid()
        uuid2 = UUIDUtils.generate_uuid()
        
        assert isinstance(uuid1, str)
        assert isinstance(uuid2, str)
        assert uuid1 != uuid2
        assert len(uuid1) > 0
        assert len(uuid2) > 0
    
    def test_generate_short_uuid(self):
        """Test short UUID generation."""
        short_uuid = UUIDUtils.generate_short_uuid()
        
        assert isinstance(short_uuid, str)
        assert len(short_uuid) < 36  # Standard UUID length
        assert len(short_uuid) > 0
    
    def test_is_valid_uuid(self):
        """Test UUID validation."""
        valid_uuid = UUIDUtils.generate_uuid()
        assert UUIDUtils.is_valid_uuid(valid_uuid) is True
        
        invalid_uuids = [
            "not-a-uuid",
            "12345",
            "",
            None
        ]
        
        for invalid_uuid in invalid_uuids:
            assert UUIDUtils.is_valid_uuid(invalid_uuid) is False


class TestRegionCodeConverter:
    """Test RegionCodeConverter class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.converter = RegionCodeConverter()
    
    def test_init(self):
        """Test converter initialization."""
        assert self.converter is not None
        assert hasattr(self.converter, 'convert')
    
    def test_convert_valid_code(self):
        """Test conversion with valid region code."""
        # Test with Beijing code
        result = self.converter.convert(110000)
        
        assert isinstance(result, dict)
        assert 'province' in result or 'city' in result
    
    def test_convert_string_code(self):
        """Test conversion with string region code."""
        result = self.converter.convert("110000")
        
        assert isinstance(result, dict)
    
    def test_convert_invalid_code(self):
        """Test conversion with invalid region code."""
        result = self.converter.convert(999999)
        
        # Should return empty dict or handle gracefully
        assert isinstance(result, dict)
    
    def test_convert_none(self):
        """Test conversion with None input."""
        result = self.converter.convert(None)
        
        assert isinstance(result, dict)
        assert result == {} or result is None


# Performance tests
class TestPerformance:
    """Performance tests for utility functions."""
    
    def test_string_utils_performance(self):
        """Test StringUtils performance with large objects."""
        large_obj = {"key_" + str(i): "value_" + str(i) for i in range(1000)}
        
        import time
        start_time = time.time()
        result = StringUtils.obj_2_json_string(large_obj)
        end_time = time.time()
        
        assert isinstance(result, str)
        assert (end_time - start_time) < 1.0  # Should complete within 1 second
    
    def test_time_utils_performance(self):
        """Test TimeUtils performance with multiple conversions."""
        import time
        
        start_time = time.time()
        for i in range(1000):
            timestamp = TimeUtils.get_current_timestamp()
            dt = TimeUtils.timestamp_to_datetime(timestamp)
            time_string = TimeUtils.datetime_to_string(dt)
        end_time = time.time()
        
        assert (end_time - start_time) < 1.0  # Should complete within 1 second


if __name__ == "__main__":
    pytest.main([__file__])
