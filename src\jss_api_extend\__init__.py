# -*- coding: utf-8 -*-
"""
JSS API Extension Package

A comprehensive Python package for social media platform APIs (Douyin, Xiaohongshu, Kuaishou) 
and DingTalk Bitable integration.

This package provides:
- adapter: API adapters for social media platforms (Douyin, Xiaohongshu, Kuaishou)
- bitable: DingTalk Bitable integration and notification services
- client: Internal API clients for third-party services
- common: Common utilities and configurations
- utils: Utility functions and helpers
"""

__version__ = "0.1.0"
__author__ = "JSS Team"
__email__ = "<EMAIL>"

# Import main classes for easy access
from .adapter import (
    <PERSON><PERSON>in<PERSON>etailHandler,
    <PERSON><PERSON>inSearchHandler,
    KuaiShouDetailHandler,
    XhsDetailHandler,
    XhsCommentHandler,
    TiktokSearchHandler,
)

from .bitable import (
    DingTalkBitableNotifier,
    TaoTianDingTalkBitableNotifier,
    DingtalkBitableFactory,
    DingTalkAlerter,
)

from .client import (
    TikhubDouyinClient,
    TikhubXhsClient,
    TikhubKuaiShouClient,
    LarkBitable,
    ServiceClient,
    DingTalkLinker,
)

# Export all main classes
__all__ = [
    # Adapter classes
    "DouyinDetailHandler",
    "DouyinSearchHandler", 
    "KuaiShouDetailHandler",
    "XhsDetailHandler",
    "XhsCommentHandler",
    "TiktokSearchHandler",
    
    # Bitable classes
    "DingTalkBitableNotifier",
    "TaoTianDingTalkBitableNotifier",
    "DingtalkBitableFactory",
    "DingTalkAlerter",
    
    # Client classes
    "TikhubDouyinClient",
    "TikhubXhsClient", 
    "TikhubKuaiShouClient",
    "LarkBitable",
    "ServiceClient",
    "DingTalkLinker",
]
