# -*- coding: utf-8 -*-
import hashlib
import time
from enum import Enum
from typing import Optional

from common.env import Env
from utils.http_utils import HttpUtils


class Environment(Enum):
    """API环境枚举"""

    LOCAL = "local"
    TEST = "test"
    PRODUCTION = "production"


class ApiType(Enum):
    # 获取文章详情
    qb_api_detail = "qb_api_detail"

    # 获取文章评论
    qb_api_comment = "qb_api_comment"


class ApiPlatform(Enum):
    xhs = "xhs"
    dy = "dy"
    wxvideo = "wxvideo"


logger_ = Env().get_main_logger()


class ServiceClient:
    """
    服务客户端类，用于调用外部API接口
    支持本地、测试、生产三种环境
    """

    # 环境配置映射
    ENV_CONFIG = {
        Environment.LOCAL: "http://127.0.0.1:9051",
        Environment.TEST: "https://apitest.jinsuosuo.com/core",
        Environment.PRODUCTION: "https://api.jinsuosuo.com/core",
    }

    def __init__(
            self,
            environment: Environment = Environment.LOCAL,
            # api_token: str = "api-token-71726a26c98b6a63a9bc9cac43ecab13",
            api_token: str = "api-token-a8cabb2bf81f04c8284c63db01514d0f",
            logger_=None,
    ):
        """
        初始化服务客户端

        Args:
            environment: API环境
            api_token: API认证令牌
            logger_: 日志记录器
        """
        self.base_url = self.ENV_CONFIG[environment]
        self.api_token = api_token

        self.logger_ = logger_
        self.environment = environment
        self.salt = 'Jss@Jss@123abc789^*^'

        self.headers = {
            "Authorization": api_token,
            "Content-Type": "application/json"
        }
        self.logger_.info(
            f"ServiceClient initialized with environment: {environment.value}, base_url: {self.base_url}"
        )

    def query_can_update(self, platform: str, limit: int = 100) -> Optional[dict]:
        """

        @param platform:
        @param limit:
        @return:
        """
        endpoint = "/api/external/queryCanUpdate"

        # 构建查询参数
        params = {
            "platform": platform,
            "limit": limit
        }

        url = self.base_url + endpoint

        self.logger_.info(f"调用queryCanUpdate接口: URL={url}, params={params}")

        try:
            response = HttpUtils.get(url=url, headers=self.headers, params=params)

            if not response:
                self.logger_.error("请求失败，响应为空")
                return None

            if not response.text.strip():
                self.logger_.error("响应内容为空")
                return None

            response.raise_for_status()
            result = response.json()

            if self.logger_:
                self.logger_.info(f"queryCanUpdate接口调用成功: {result}")

            return result

        except Exception as e:
            if self.logger_:
                self.logger_.error(f"queryCanUpdate接口调用异常: {str(e)}")
            return None

    def query_table_to_update(self):
        """
            api 表格数据更新
        @return:
        """
        try:
            endpoint = "/api/external/query/bitable/task"
            _url = self.base_url + endpoint

            self.logger_.info(f"start request url : {endpoint}")
            response = HttpUtils.get(url=_url, headers=self.headers)

            if not response:
                self.logger_.error("请求失败，响应为空")
                return None

            if not response.text.strip():
                self.logger_.error("响应内容为空")
                return None

            response.raise_for_status()
            return response.json()

        except Exception as e:
            self.logger_.info(f"error {e}")

    def invoke_api_quota(self,
                         user_id: int,
                         tenant: str,
                         api_type: ApiType,
                         platform_type: str):
        """
            扣费接口
        """
        endpoint = "/quota/statistic/api/invoke"
        timestamp = int(time.time() * 1000)
        params = {
            "userId": user_id,
            "tenant": tenant,
            "apiType": api_type.value,
            "platformType": platform_type,
            "timestamp": timestamp,
            "sign": self.__sign(user_id, tenant, api_type.value, platform_type, timestamp)
        }

        url = self.base_url + endpoint

        if self.logger_:
            self.logger_.info(f"调用query_api_quota接口: URL={url}, params={params}")

        try:
            _response = HttpUtils.post_v2(url=url, headers=self.headers, data=dict(params))

            if not _response:
                self.logger_.error("请求失败，响应为空")
                return None

            if not _response.text.strip():
                self.logger_.error("响应内容为空")
                return None

            _response.raise_for_status()
            result = _response.json()

            self.logger_.info(f"query_api_quota接口调用成功: {result}")
            return result

        except Exception as e:
            self.logger_.error(f"query_api_quota接口调用异常: {str(e)}")
            return None

    def invoke_api_quota_batch(self,
                               user_id: int,
                               tenant: str,
                               count: int,
                               api_type: ApiType,
                               platform_type: ApiPlatform):
        """
            扣费接口
        """
        endpoint = "/quota/statistic/api/invokex"
        timestamp = int(time.time() * 1000)
        params = {
            "userId": user_id,
            "tenant": tenant,
            "apiType": api_type.value,
            "count": count,
            "platformType": platform_type.value,
            "timestamp": timestamp,
            "sign": self.__sign_x(user_id, tenant, api_type.value, count, platform_type.value, timestamp)
        }

        url = self.base_url + endpoint

        if self.logger_:
            self.logger_.info(f"调用query_api_quota接口: URL={url}, params={params}")

        try:
            response = HttpUtils.post_v2(url=url, headers=self.headers, data=params)

            if not response:
                self.logger_.error("请求失败，响应为空")
                return None

            if not response.text.strip():
                self.logger_.error("响应内容为空")
                return None

            response.raise_for_status()
            result = response.json()

            self.logger_.info(f"query_api_quota接口调用成功: {result}")
            return result

        except Exception as e:
            self.logger_.error(f"query_api_quota接口调用异常: {str(e)}")
            return None

    def __sign(self,
               user_id: int,
               tenant: str,
               api_type: str,
               platform_type: str,
               timestamp: int
               ) -> str:
        """
        生成签名
        @return: MD5加密后的签名字符串
        """
        raw_str = f"UserId:{user_id};Tenant:{tenant};platform:{platform_type};api_type:{api_type};ts:{timestamp};{self.salt}"

        return hashlib.md5(raw_str.encode('utf-8')).hexdigest()

    def __sign_x(self,
                 user_id: int,
                 tenant: str,
                 api_type: str,
                 count: int,
                 platform_type: str,
                 timestamp: int
                 ) -> str:
        """
        生成签名
        @return: MD5加密后的签名字符串
        """
        raw_str = f"UserId:{user_id};Tenant:{tenant};platform:{platform_type};api_type:{api_type};count:{count};ts:{timestamp};{self.salt}"

        return hashlib.md5(raw_str.encode('utf-8')).hexdigest()

    def is_response_success(self, response: dict) -> bool:
        """
        检查API响应是否成功

        Args:
            response: API响应数据

        Returns:
            是否成功
        """
        if not response:
            return False
        return response.get("code") == "success"

    def get_response_data(self, response: dict) -> Optional[list]:
        """
        从API响应中提取结果数据

        Args:
            response: API响应数据

        Returns:
            结果数据列表或None
        """
        if not self.is_response_success(response):
            return None
        return response.get("result", [])


    def mark_update(self, id: int) -> Optional[dict]:
        """
        标记更新成功的接口
        """
        endpoint = "/api/external/markUpdated"

        # 构建查询参数
        params = {"id": id}

        url = self.base_url + endpoint

        if self.logger_:
            self.logger_.info(f"调用markUpdate接口: URL={url}, params={params}")

        try:
            response = HttpUtils.get(url=url, headers=self.headers, params=params)

            if not response:
                if self.logger_:
                    self.logger_.error("markUpdate请求失败，响应为空")
                return None

            if not response.text.strip():
                if self.logger_:
                    self.logger_.error("markUpdate响应内容为空")
                return None

            response.raise_for_status()
            result = response.json()

            if self.logger_:
                self.logger_.info(f"markUpdate接口调用成功: {result}")

            return result

        except Exception as e:
            if self.logger_:
                self.logger_.error(f"markUpdate接口调用异常: {str(e)}")
            return None


if __name__ == "__main__":
    service = ServiceClient(environment=Environment.LOCAL, logger_=logger_)
    _response: dict =  service.invoke_api_quota_batch(user_id=10, tenant="9afbe002-8adb-4f27-bb1c-f57ae715bb80", api_type=ApiType.qb_api_detail,
                             platform_type=ApiPlatform.xhs, count=10)
    print(service.is_response_success(_response))

    # pso = service.mark_update()
    # print(pso)
