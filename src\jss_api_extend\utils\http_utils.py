# -*- coding=utf-8 -*-

import requests


class HttpUtils(object):
    @staticmethod
    def post(url, headers=None, params=None, data=None) -> requests.Response:
        try:
            res = requests.post(url=url, headers=headers, params=params, data=data, timeout=15)
            return res
        except Exception as e:
            return None

    @staticmethod
    def get(url, headers=None, params=None, data=None) -> requests.Response:
        try:
            res = requests.get(url=url, headers=headers, params=params, data=data, timeout=15)
            return res
        except Exception as e:
            return None

    @staticmethod
    def post_v2(url, headers=None, params=None, data=None) -> requests.Response:
        try:
            res = requests.post(url=url, headers=headers, params=params, json=data, timeout=15)
            return res
        except Exception as e:
            # 最好打印出异常，便于调试
            print(f"HttpUtils.post exception: {e}")
            return None
