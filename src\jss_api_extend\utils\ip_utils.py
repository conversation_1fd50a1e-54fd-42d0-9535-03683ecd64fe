# -*- encoding:utf-8 -*-

import socket

import requests


class IPUtils:
    """
    ip相关工具类
    """

    @staticmethod
    def get_window_ip_address():
        host_name = socket.gethostname()
        ip = socket.gethostbyname(host_name)
        return ip

    @staticmethod
    def get_internet_ip_address():
        return requests.get('http://ifconfig.me/ip', timeout=1).text.strip()

    @staticmethod
    def get_ip_address(ifname='eth0'):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            ip = s.getsockname()[0]
        finally:
            s.close()
        return ip

        '''
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        return socket.inet_ntoa(fcntl.ioctl(
            s.fileno(),
            0x8915,
            struct.pack('256s', ifname[:15]))[20:24])
        '''
