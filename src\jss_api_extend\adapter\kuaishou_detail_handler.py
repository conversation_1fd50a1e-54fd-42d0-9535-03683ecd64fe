# -*- coding: utf-8 -*-

import time
from typing import Optional

from dateutil import parser
from tikhub import Client

from client.tikhub_kuaishou_client import TikhubKuaiShouClient
from adapter.models.author_work import AuthorWork
from utils.region_code_converter import RegionCodeConverter
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils
from utils.url_utils import UrlUtils
from config.tikhub_config import tikhub_config


class KuaiShouDetailHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.client = Client(base_url=tikhub_config.get('base_url'),
                             api_key=tikhub_config.get('api_key'),
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)
        self._converter = RegionCodeConverter()

        self.tikhub_ks_client = TikhubKuaiShouClient(logger_=logger_)

    def query_article_detail_app_v1(self, work_url):
        note_id = UrlUtils.get_kuaishou_work_id(work_url)
        flag, work_article = self._query_article_detail_by_tikhub(note_id, work_url)
        if flag:
            return work_article

        return None

    def _query_article_detail_by_tikhub(self, note_id, work_url):
        article = self._query_article_detail_by_tikhub_inner(note_id)
        if article is None:
            time.sleep(1)
            article = self._query_article_detail_by_tikhub_inner(note_id)

        if article is None:
            time.sleep(1)
            article = self._query_article_detail_by_tikhub_inner(note_id)

        if article is None:
            return False, None

        return True, self._generate_tiktok_article(note_id, work_url, article.get("data"))

    def _query_article_detail_by_tikhub_inner(self, note_id):
        article = self.tikhub_ks_client.app_fetch_one_video_v1(note_id)
        if not article or "data" not in article:
            return None

        code = article.get("code")
        if code != 200:
            return None

        return article

    def _generate_tiktok_article(self, note_id, work_url, data):
        try:
            notes = data.get("photos")
            if notes is None:
                return None

            note = notes[0]
            if note is None:
                return None

            publish_day = ''
            author_avatar = ''

            author_name = self._parse_json_str(note, 'user_name')

            share_info = self._parse_json_str(note, 'share_info')
            author_id = share_info.split('&')[0].split('=')[1]
            author_url = "https://www.kuaishou.com/profile/" + author_id
            headurls = note.get("headurls")
            if len(headurls) > 0:
                head_url = headurls[0]
                author_avatar = self._parse_json_str(head_url, "url")

            work_id = note_id
            url = work_url

            title = self._parse_json_str(note, 'caption')
            content = self._parse_json_str(note, 'caption')

            read_count = note.get('view_count', 0)
            collect_count = note.get('collect_count', 0)
            comment_count = note.get('comment_count', 0)
            share_count = note.get('share_count', 0)
            like_count = note.get('like_count', 0)

            # city_code = note.get('location', '')
            # ip_location = self._convert_code_to_name(city_code)

            publish_time = note.get('time')
            try:
                date_obj = parser.parse(publish_time)
                publish_day = date_obj.strftime("%Y-%m-%d")
            except Exception as e2:
                print(e2)

            thumbnail_link = ''
            video_url = ''
            try:
                cover_url_list = note.get('cover_urls')
                thumbnail_link = cover_url_list[0]['url']
            except Exception as e:
                pass

            return AuthorWork(_id=0, platform='dy', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls=thumbnail_link, video_urls=video_url,
                              music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip='', read_count=read_count,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")
            return None

    def _generate_qingbo_article(self, item: dict) -> Optional[AuthorWork]:
        try:
            author_name = item.get('media_name', '')
            author_id = item.get('media_identity', '')
            author_avatar = item.get('media_picurl', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = item.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = item.get('news_title', '')
            content = item.get('news_content')
            collect_count = item.get('news_collect_cnt')
            comment_count = item.get('news_comment_count')
            share_count = item.get('news_reposts_count')
            like_count = item.get('news_like_count')
            publish_time = item.get('news_posttime')

            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")
            thumbnail_link = item.get('news_headimg_url', '')

            return AuthorWork(_id=0, platform='dy', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )
        except Exception as e:
            self.logger_.error(f"_generate_qingbo_article {StringUtils.obj_2_json_string(item)}, {e}")
            return None

    def _convert_code_to_name(self, code):
        if code is None:
            return ""

        d = self._converter.convert(code)
        province = d.get("province", "")
        city = d.get("city", "")

        if len(city) > 0:
            return city

        return province

    def _parse_json_str(self, obj, key):
        if obj is None:
            return ""

        return obj.get(key, "")


if __name__ == "__main__":
    from common.env import Env

    logger_ = Env().get_main_logger()
    ks_handler = KuaiShouDetailHandler(logger_)

    work_article = ks_handler.query_article_detail_app_v1('https://www.kuaishou.com/short-video/3xk4g2yryfjpin6?authorId=3xntsdjqqfa9n29&streamSource=brilliant&hotChannelId=00&area=brilliantxxcarefully')
    print(work_article)
