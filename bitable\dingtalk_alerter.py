# -*- coding: utf-8 -*-

import json

from utils.http_utils import HttpUtils


class DingTalkAlerter:
    def __init__(self):
        self.ding_talk_alert_url = "https://oapi.dingtalk.com/robot/send?access_token=1fe8be2a320a14ddd858c56eef93b4dc1f1349f51cce7134a694b410f297c3b6"

    def send(self, msg):
        headers = {
            'Content-Type': 'application/json',
        }

        data = {
            "msgtype": "text",
            "text": {
                "content": "异常报警：" + msg
            }
        }

        # 把data转成json字符串
        json_data = json.dumps(data)
        HttpUtils.post(self.ding_talk_alert_url, headers=headers, data=json_data)
